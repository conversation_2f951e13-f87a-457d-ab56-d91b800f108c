﻿using AIEvent.Application.DTOs.Common;
using AIEvent.Domain.Enums;

namespace AIEvent.Application.DTOs.User
{
    public class UserResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public List<string> Roles { get; set; } = [];
        public string? Address { get; set; }
        public string? AvatarImgUrl { get; set; }
    }

    public class UserOrganizerResponse
    {
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
    }
}

﻿using System.ComponentModel.DataAnnotations;

namespace AIEvent.Domain.Enums
{
    public enum EventExperienceLevel
    {
        [Display(Name = "<PERSON><PERSON><PERSON> bắt đầu (0-1 năm)")]
        Beginner = 1,

        [Display(Name = "<PERSON><PERSON> kinh nghiệm (1-3 năm)")]
        Intermediate = 2,

        [Display(Name = "<PERSON><PERSON><PERSON><PERSON> kinh nghiệm (3-5 năm)")]
        Experienced = 3,

        [Display(Name = "Chuyên gia (5+ năm)")]
        Expert = 4
    }
}
